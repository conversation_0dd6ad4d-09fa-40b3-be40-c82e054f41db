from dataclasses import dataclass
import math


@dataclass
class InventoryItem:
    name: str
    unit_price: float
    quantity_on_hand: int = 0


print(InventoryItem("chou", 98.3))


@dataclass
class Circle:
    radius: float

    def area(self):
        return math.pi * pow(self.radius, 2)


print(Circle(2).area())


@dataclass
class Author:
    name: str
    email: str


@dataclass
class Book:
    title: str
    isbn: str
    author: Author


author = Author("chou", "<EMAIL>")
book = Book("laugh", "xxx", author)
print(book)
